<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Chatbot BPS Pontianak - Setup</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .step { 
            background: rgba(255, 255, 255, 0.1); 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
            border-left: 4px solid #4CAF50;
        }
        .code { 
            background: rgba(0, 0, 0, 0.3); 
            padding: 15px; 
            border-radius: 5px; 
            font-family: 'Courier New', monospace; 
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #FFC107;
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            border-left-color: #2196F3;
        }
        .demo-section {
            background: rgba(156, 39, 176, 0.2);
            border-left-color: #9C27B0;
            text-align: center;
        }
        #qrcode {
            max-width: 200px;
            height: auto;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: white;
            padding: 10px;
            margin: 20px auto;
            display: block;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>
        
        <div class="step demo-section">
            <h3>📱 Demo QR Code</h3>
            <p>This is a demonstration of the QR code interface:</p>
            <img id="qrcode" src="" alt="Demo QR Code" style="display: none;" />
            <div id="qr-loading">⏳ Loading demo QR code...</div>
            <button onclick="loadDemoQR()">🔄 Load Demo QR</button>
            <p><small>Note: This is not a functional WhatsApp QR code</small></p>
        </div>
        
        <div class="step info">
            <h3>📋 Setup Instructions</h3>
            <p>This chatbot needs to run locally to connect to WhatsApp Web. Follow these steps:</p>
        </div>

        <div class="step">
            <h3>1. Clone the Repository</h3>
            <div class="code">git clone &lt;repository-url&gt;<br>cd whatsapp-chatbot-bps-6171</div>
        </div>

        <div class="step">
            <h3>2. Install Dependencies</h3>
            <div class="code">npm install</div>
        </div>

        <div class="step">
            <h3>3. Setup Environment Variables</h3>
            <p>Create a <code>.env</code> file with:</p>
            <div class="code">
                API_KEY=your_gemini_api_key<br>
                SPREADSHEET_ID=your_google_sheets_id<br>
                NODE_ENV=development
            </div>
        </div>

        <div class="step">
            <h3>4. Add Google Sheets Credentials</h3>
            <p>Place your <code>credentials.json</code> file in the project root.</p>
        </div>

        <div class="step">
            <h3>5. Run the Bot</h3>
            <div class="code">npm run dev</div>
            <p>Then visit <code>http://localhost:3000</code> to scan the QR code with WhatsApp.</p>
        </div>

        <div class="step warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li>The bot requires a persistent connection to WhatsApp Web</li>
                <li>It must run on a server or local machine, not on Netlify</li>
                <li>This deployment shows setup instructions only</li>
                <li>For production, consider using a VPS or dedicated server</li>
            </ul>
        </div>

        <div class="step info">
            <h3>📞 Contact Information</h3>
            <p>For support with BPS Pontianak services, please contact our official channels.</p>
        </div>
    </div>

    <script>
        function loadDemoQR() {
            fetch('/.netlify/functions/api/qr')
                .then(response => response.json())
                .then(data => {
                    if (data.qrCode) {
                        const qrImg = document.getElementById('qrcode');
                        qrImg.src = data.qrCode;
                        qrImg.style.display = 'block';
                        document.getElementById('qr-loading').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading demo QR:', error);
                    document.getElementById('qr-loading').innerHTML = '❌ Failed to load demo QR';
                });
        }

        // Auto-load demo QR on page load
        window.addEventListener('load', loadDemoQR);
    </script>
</body>
</html>
