# WhatsApp Chatbot BPS Pontianak

A WhatsApp chatbot for BPS (Badan Pusat Statistik) Pontianak that integrates with Google Gemini AI and Google Sheets for automated responses and data logging.

## 🌟 Features

- **WhatsApp Integration**: Connects to WhatsApp Web for message handling
- **AI-Powered Responses**: Uses Google Gemini AI for intelligent responses
- **Google Sheets Integration**: Logs conversations and retrieves context from spreadsheets
- **QR Code Interface**: Web interface for easy WhatsApp connection
- **Dual Deployment**: Supports both local development and Netlify deployment

## 🚀 Deployment Options

### Option 1: Local Development (Recommended for Production)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd whatsapp-chatbot-bps-6171
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment variables**
   Create a `.env` file:
   ```env
   API_KEY=your_gemini_api_key
   SPREADSHEET_ID=your_google_sheets_id
   NODE_ENV=development
   ```

4. **Add Google Sheets credentials**
   Place your `credentials.json` file in the project root.

5. **Run the bot**
   ```bash
   npm run dev
   ```

6. **Connect WhatsApp**
   - Visit `http://localhost:3000`
   - Scan the QR code with WhatsApp
   - Start chatting!

### Option 2: Netlify Deployment (Demo/Instructions Only)

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=<your-repo-url>)

**Note**: The Netlify deployment only shows setup instructions and a demo QR code interface. The actual WhatsApp functionality requires a persistent server connection and must run locally or on a VPS.

## 📋 Requirements

- Node.js 18+
- Google Gemini API key
- Google Sheets API credentials
- WhatsApp account

### Tech stack

1. [NodeJS](https://nodejs.org/id)
2. [Whatsapp-webjs](https://wwebjs.dev/)
3. [qrcode-terminal](https://www.npmjs.com/package/qrcode-terminal)
4. [google/generative-ai (Gemini)](https://www.npmjs.com/package/@google/generative-ai)
5. [nodemon](https://www.npmjs.com/package/nodemon)

### Steps

- [ ] Create a Node.js project:

    ```bash
    npm init
    ```

- [ ] Install all libraries:

    ```bash
    npm i whatsapp-web.js qrcode-terminal @google/generative-ai nodemon
    ```

- [ ] Generate a Gemini API key: [https://ai.google.dev/](https://ai.google.dev/)
- [ ] Code `generateContent` from Gemini
- [ ] Code WhatsappJS to handle WhatsApp messages
- [ ] Test!

---

### How to clone and run this project

1. **Clone the repository:**

    ```bash
    git clone https://github.com/yourusername/WhatsappChatbot.git
    ```

2. **Navigate to the project folder:**

    ```bash
    cd WhatsappChatbot
    ```

3. **Install dependencies:**

    ```bash
    npm install
    ```

4. **Set up environment variables:**

   - Create a `.env` file and add your [Google Gemini API Key](https://ai.google.dev/) in the following format:

    ```bash
    GOOGLE_GEMINI_API_KEY=your-api-key-here
    ```

5. **Run the project:**

    ```bash
    npm start
    ```

6. **Scan the QR code:**
   - When you run the project, a QR code will be generated in the terminal. Open WhatsApp on your phone and scan the QR code to connect the bot to your WhatsApp account.

7. **Interact with the bot:**
   - Send a message to your bot and watch it respond using the Gemini AI and WhatsApp integration!

8. **For development:**
   - If you want to make changes and automatically restart the app, use `nodemon`:

    ```bash
    npm run dev
    ```

---

Now you're ready to start building and testing your WhatsApp chatbot!
