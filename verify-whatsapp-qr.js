// Definitive test to verify WhatsApp QR code generation
const { Client, LocalAuth } = require("whatsapp-web.js");
const QRCode = require("qrcode");
const fs = require("fs");

console.log("🔬 DEFINITIVE WhatsApp QR Code Verification Test\n");

// Clean start - remove old auth data
if (fs.existsSync('.wwebjs_auth_verify')) {
  fs.rmSync('.wwebjs_auth_verify', { recursive: true, force: true });
  console.log("🧹 Cleaned old auth data");
}

const client = new Client({
  authStrategy: new LocalAuth({
    dataPath: "./.wwebjs_auth_verify",
  }),
  puppeteer: {
    headless: true,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu",
    ],
  },
  webVersionCache: {
    type: "remote",
    remotePath: "https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html",
  },
});

let qrCount = 0;

client.on("qr", async (qr) => {
  qrCount++;
  console.log(`\n🎯 QR CODE #${qrCount} RECEIVED:`);
  console.log("📏 Length:", qr.length);
  console.log("🔤 First 30 chars:", qr.substring(0, 30));
  console.log("🔤 Last 30 chars:", qr.substring(qr.length - 30));
  
  // Detailed analysis
  const hasAt = qr.includes('@');
  const hasComma = qr.includes(',');
  const startsWithNumber = /^[0-9]@/.test(qr);
  const hasBase64Chars = /[A-Za-z0-9+/=]/.test(qr);
  const isLongEnough = qr.length > 100;
  
  console.log("\n🔍 ANALYSIS:");
  console.log("  ✓ Contains '@':", hasAt);
  console.log("  ✓ Contains ',':", hasComma);
  console.log("  ✓ Starts with number@:", startsWithNumber);
  console.log("  ✓ Has base64 characters:", hasBase64Chars);
  console.log("  ✓ Length > 100:", isLongEnough);
  
  const isRealWhatsApp = hasAt && hasComma && startsWithNumber && hasBase64Chars && isLongEnough;
  
  if (isRealWhatsApp) {
    console.log("\n🎉 VERDICT: This IS a REAL WhatsApp QR code!");
    console.log("📱 This QR code WILL work with WhatsApp scanning");
  } else {
    console.log("\n❌ VERDICT: This is NOT a real WhatsApp QR code");
    console.log("📱 This QR code will NOT work with WhatsApp");
  }

  try {
    // Generate QR code image
    await QRCode.toFile(`verify-qr-${qrCount}.png`, qr, {
      width: 400,
      margin: 2,
      errorCorrectionLevel: "M",
    });
    
    // Save raw data
    fs.writeFileSync(`verify-qr-${qrCount}-data.txt`, qr);
    
    console.log(`💾 Saved as verify-qr-${qrCount}.png and verify-qr-${qrCount}-data.txt`);
    
    // Generate a comparison demo QR code
    const demoData = "Demo QR Code - Not WhatsApp";
    await QRCode.toFile(`demo-qr-${qrCount}.png`, demoData, {
      width: 400,
      margin: 2,
      errorCorrectionLevel: "M",
    });
    
    console.log("📊 Visual comparison:");
    console.log(`  - verify-qr-${qrCount}.png = REAL WhatsApp QR (complex, dense)`);
    console.log(`  - demo-qr-${qrCount}.png = Simple demo QR (simple, sparse)`);
    
  } catch (error) {
    console.error("❌ Error saving QR code:", error);
  }

  // Stop after first QR code for analysis
  if (qrCount === 1) {
    setTimeout(() => {
      console.log("\n🏁 Test completed. Check the generated files:");
      console.log("1. Open verify-qr-1.png - this is what you should see in browser");
      console.log("2. Compare with demo-qr-1.png - notice the difference");
      console.log("3. The real WhatsApp QR should be much more complex and dense");
      console.log("\n💡 If verify-qr-1.png looks simple, there's an issue with WhatsApp client");
      console.log("💡 If verify-qr-1.png looks complex, then QR codes ARE working correctly");
      process.exit(0);
    }, 3000);
  }
});

client.on("loading_screen", (percent, message) => {
  console.log(`⏳ Loading: ${percent}% - ${message}`);
});

client.on("ready", () => {
  console.log("✅ WhatsApp client ready - already authenticated");
  if (qrCount === 0) {
    console.log("ℹ️ No QR code needed (already logged in)");
    setTimeout(() => process.exit(0), 2000);
  }
});

client.on("auth_failure", (msg) => {
  console.error("❌ Authentication failed:", msg);
  process.exit(1);
});

// Timeout after 60 seconds
setTimeout(() => {
  console.log("⏰ Test timeout - no QR code received in 60 seconds");
  console.log("🔧 This might indicate an issue with WhatsApp Web.js setup");
  process.exit(1);
}, 60000);

console.log("🚀 Starting WhatsApp client...");
console.log("⏳ Waiting for QR code generation...");

client.initialize().catch((error) => {
  console.error("❌ Failed to initialize client:", error);
  process.exit(1);
});
