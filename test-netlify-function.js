// Test script for Netlify function
const { handler } = require('./netlify/functions/api.js');

async function testFunction() {
  console.log('🧪 Testing Netlify function...\n');

  // Test QR endpoint
  const qrEvent = {
    path: '/.netlify/functions/api/qr',
    httpMethod: 'GET',
    headers: {},
    queryStringParameters: null,
    body: null
  };

  console.log('Testing /qr endpoint...');
  try {
    const qrResult = await handler(qrEvent, {});
    console.log('✅ QR endpoint status:', qrResult.statusCode);
    
    if (qrResult.statusCode === 200) {
      const data = JSON.parse(qrResult.body);
      console.log('✅ QR code generated, length:', data.qrCode ? data.qrCode.length : 'N/A');
      console.log('✅ Demo flag:', data.demo);
    } else {
      console.log('❌ QR endpoint failed:', qrResult.body);
    }
  } catch (error) {
    console.error('❌ QR endpoint error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test status endpoint
  const statusEvent = {
    path: '/.netlify/functions/api/status',
    httpMethod: 'GET',
    headers: {},
    queryStringParameters: null,
    body: null
  };

  console.log('Testing /status endpoint...');
  try {
    const statusResult = await handler(statusEvent, {});
    console.log('✅ Status endpoint status:', statusResult.statusCode);
    
    if (statusResult.statusCode === 200) {
      const data = JSON.parse(statusResult.body);
      console.log('✅ Environment:', data.environment);
      console.log('✅ Has QR Code:', data.hasQRCode);
    } else {
      console.log('❌ Status endpoint failed:', statusResult.body);
    }
  } catch (error) {
    console.error('❌ Status endpoint error:', error.message);
  }

  console.log('\n✨ Test completed!');
}

testFunction();
