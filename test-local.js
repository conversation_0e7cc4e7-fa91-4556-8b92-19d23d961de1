// Simple test to verify the application works in development mode
const express = require('express');
const QRCode = require('qrcode');

console.log('🧪 Testing local development setup...');

// Test environment detection
process.env.NODE_ENV = 'development';
const isNetlify = process.env.NETLIFY === 'true';
const isDevelopment = process.env.NODE_ENV !== 'production' && !isNetlify;

console.log('Environment detection:');
console.log('- isNetlify:', isNetlify);
console.log('- isDevelopment:', isDevelopment);

// Test QR code generation
async function testQRGeneration() {
  try {
    const testQR = await QRCode.toDataURL('Test QR Code for WhatsApp Bot');
    console.log('✅ QR code generation: SUCCESS');
    console.log('QR code length:', testQR.length);
  } catch (error) {
    console.error('❌ QR code generation: FAILED', error.message);
  }
}

// Test Express setup
function testExpress() {
  try {
    const app = express();
    app.use(express.json());
    
    app.get('/test', (req, res) => {
      res.json({ status: 'ok', message: 'Test endpoint working' });
    });
    
    console.log('✅ Express setup: SUCCESS');
  } catch (error) {
    console.error('❌ Express setup: FAILED', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('\n🔍 Running tests...\n');
  
  testExpress();
  await testQRGeneration();
  
  console.log('\n✨ Test completed!');
  console.log('\nNext steps:');
  console.log('1. Run "npm run dev" to start the development server');
  console.log('2. Visit http://localhost:3000 to see the QR code interface');
  console.log('3. For Netlify deployment, push to your repository and deploy');
}

runTests();
