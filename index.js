const fs = require("fs");
const path = require("path");
const express = require("express");
const QRCode = require("qrcode");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const { google } = require("googleapis");
require("dotenv").config();

// Environment detection
const isNetlify = process.env.NETLIFY === "true";
const isDevelopment = process.env.NODE_ENV !== "production" && !isNetlify;

console.log("🔧 Environment Configuration:");
console.log("- NODE_ENV:", process.env.NODE_ENV);
console.log("- NETLIFY:", process.env.NETLIFY);
console.log("- isNetlify:", isNetlify);
console.log("- isDevelopment:", isDevelopment);

// --- Gemini AI Init ---
const genAI = new GoogleGenerativeAI(process.env.API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

// --- Express Server Setup ---
const app = express();
const PORT = process.env.PORT || 3001;

app.use(express.static(path.join(__dirname)));
app.use(express.json());

// Global variables for QR code and client state
let currentQRCode = null;
let clientReady = false;
let client = null;

// Only initialize WhatsApp client in development/local environment
if (isDevelopment) {
  try {
    const { Client, LocalAuth } = require("whatsapp-web.js");
    const qrcode = require("qrcode-terminal");

    // --- WhatsApp Client ---
    client = new Client({
      authStrategy: new LocalAuth(),
      puppeteer: {
        headless: true,
        args: ["--no-sandbox", "--disable-setuid-sandbox"],
      },
    });

    console.log("✅ WhatsApp Web.js initialized successfully");
  } catch (error) {
    console.warn("⚠️ WhatsApp Web.js not available:", error.message);
    console.log("📝 Note: Run 'npm install' to download required dependencies");
    client = null;
  }
}

// WhatsApp client event handlers (only in development)
if (isDevelopment && client) {
  client.on("qr", async (qr) => {
    console.log("📱 Scan this QR code with WhatsApp:");

    try {
      // Generate QR code as base64 for in-memory storage
      currentQRCode = await QRCode.toDataURL(qr, {
        width: 300,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Also save as file for local development
      await QRCode.toFile("qr.png", qr, {
        width: 300,
        margin: 2,
      });

      console.log("🖼️ QR code generated successfully!");
      console.log("🌐 Visit http://localhost:" + PORT + " to scan the QR code");
    } catch (err) {
      console.error("❌ Failed to generate QR code:", err);
    }
  });

  client.once("ready", () => {
    console.log("✅ WhatsApp bot is ready!");
    clientReady = true;
    currentQRCode = null; // Clear QR code when ready
  });

  client.on("disconnected", (reason) => {
    console.log("❌ WhatsApp client disconnected:", reason);
    clientReady = false;
    currentQRCode = null;
  });

  client.on("auth_failure", (msg) => {
    console.error("❌ Authentication failed:", msg);
    clientReady = false;
    currentQRCode = null;
  });
}

// --- Google Sheets Config ---
const spreadsheetId = process.env.SPREADSHEET_ID;

function getIndonesiaDateTime() {
  const date = new Date();
  const options = {
    timeZone: "Asia/Jakarta",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  };

  const formatter = new Intl.DateTimeFormat("en-GB", options);
  const parts = formatter.formatToParts(date);

  const dateParts = {};
  for (const { type, value } of parts) {
    if (type !== "literal") {
      dateParts[type] = value;
    }
  }

  return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
}

// --- Real-Time RAG from Google Sheets ---
async function retrieveContext(prompt) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  const range = "RAG!A2:B";
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
  });

  const rows = response.data.values || [];
  const promptLower = prompt.toLowerCase();
  const matchedAnswers = [];

  for (const [keyword, answer] of rows) {
    const keywordLower = keyword.toLowerCase();
    if (promptLower.includes(keywordLower)) {
      matchedAnswers.push(`• ${answer}`);
    }
  }

  return matchedAnswers.length > 0
    ? matchedAnswers.join("\n\n")
    : "Maaf, saya hanya dapat membantu terkait informasi BPS Kota Pontianak. Silakan hubungi kontak resmi kami.";
}

// --- Append to Google Sheets (MESSAGE Sheet) ---
async function logMessageToSheet(sender, message, response) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: "MESSAGE!A2:D",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: [[sender, message, response, getIndonesiaDateTime()]],
    },
  });
}

// --- Message Handler (only in development) ---
if (isDevelopment && client) {
  client.on("message_create", async (message) => {
    try {
      if (message.fromMe) return;

      const prompt = message.body.toString().trim();
      if (!prompt) return;

      const sender = message.from;

      const context = await retrieveContext(prompt);

      const result = await model.generateContent(
        `Sebagai admin WhatsApp BPS Kota Pontianak, jawab pertanyaan ini berdasarkan konteks berikut:\n\n${context}\n\nPertanyaan:\n${prompt}`
      );
      const response = result.response.text();

      console.log("🟢 Replied to:", prompt);
      await client.sendMessage(message.from, response);
      console.log("🟢 Replied with:", response);

      await logMessageToSheet(sender, prompt, response);
    } catch (err) {
      console.error("❌ Error:", err);
      await client.sendMessage(
        message.from,
        "⚠️ Maaf, terjadi kesalahan. Silakan coba lagi nanti."
      );
    }
  });
}

// API Routes
app.get("/api/status", (req, res) => {
  res.json({
    environment: isNetlify ? "netlify" : "development",
    clientReady: clientReady,
    hasQRCode: !!currentQRCode,
  });
});

app.get("/api/qr", (req, res) => {
  if (currentQRCode) {
    res.json({ qrCode: currentQRCode });
  } else {
    res.status(404).json({ error: "No QR code available" });
  }
});

// Main route - QR Code Interface
app.get("/", (req, res) => {
  res.send(`
    <html>
      <head>
        <title>WhatsApp Chatbot - Scan QR Code</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 500px;
            width: 100%;
          }
          #qrcode {
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 20px;
            background: white;
            border-radius: 15px;
            margin: 20px 0;
            max-width: 300px;
            height: auto;
          }
          .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
          }
          .loading { background: rgba(255, 193, 7, 0.3); }
          .ready { background: rgba(76, 175, 80, 0.3); }
          .error { background: rgba(244, 67, 54, 0.3); }
          button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
          }
          button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
          }
          .qr-placeholder {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>
          <h2>📱 Scan QR Code with WhatsApp</h2>

          <div id="status" class="status loading">
            🔄 Initializing WhatsApp connection...
          </div>

          <div id="qr-container">
            <img id="qrcode" alt="QR Code" style="display: none;" />
            <div id="qr-placeholder" class="qr-placeholder">
              <p>⏳ Generating QR code...</p>
              <p>Please wait a moment.</p>
            </div>
          </div>

          <button onclick="refreshQR()">🔄 Refresh QR Code</button>
          <button onclick="checkStatus()">📊 Check Status</button>

          <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <div id="qr-instructions">
              <p>📋 Instructions:</p>
              <ol style="text-align: left; max-width: 300px;">
                <li>Open WhatsApp on your phone</li>
                <li>Go to Settings → Linked Devices</li>
                <li>Tap "Link a Device"</li>
                <li>Scan the QR code above</li>
              </ol>
            </div>

            <div id="demo-warning" style="display: none; background: rgba(255, 193, 7, 0.2); padding: 15px; border-radius: 10px; margin-top: 15px;">
              <p>⚠️ <strong>Demo QR Code</strong></p>
              <p style="font-size: 12px;">This is a demonstration QR code. For actual WhatsApp bot functionality, please run the application locally with all dependencies installed.</p>
            </div>
          </div>
        </div>

        <script>
          let checkInterval;

          function checkStatus() {
            fetch('/api/status')
              .then(response => response.json())
              .then(data => {
                const statusDiv = document.getElementById('status');
                if (data.clientReady) {
                  statusDiv.className = 'status ready';
                  statusDiv.innerHTML = '✅ WhatsApp bot is ready and connected!';
                  document.getElementById('qr-container').style.display = 'none';
                  document.getElementById('demo-warning').style.display = 'none';
                  clearInterval(checkInterval);
                } else if (data.hasQRCode) {
                  statusDiv.className = 'status loading';
                  statusDiv.innerHTML = '📱 Please scan the QR code with WhatsApp';
                  loadQRCode();
                  // Show demo warning if this is likely a demo QR code
                  if (data.environment === 'netlify' || !data.clientReady) {
                    document.getElementById('demo-warning').style.display = 'block';
                  }
                } else {
                  statusDiv.className = 'status loading';
                  statusDiv.innerHTML = '🔄 Generating QR code...';
                }
              })
              .catch(error => {
                console.error('Status error:', error);
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = '❌ Connection error - trying to load QR code directly';
                document.getElementById('demo-warning').style.display = 'block';
                loadQRCodeFallback();
              });
          }

          function loadQRCode() {
            fetch('/api/qr')
              .then(response => {
                if (!response.ok) {
                  throw new Error('QR API not available');
                }
                return response.json();
              })
              .then(data => {
                if (data.qrCode) {
                  const qrImg = document.getElementById('qrcode');
                  qrImg.src = data.qrCode;
                  qrImg.style.display = 'block';
                  document.getElementById('qr-placeholder').style.display = 'none';
                }
              })
              .catch(error => {
                console.error('QR Code API error:', error);
                loadQRCodeFallback();
              });
          }

          function loadQRCodeFallback() {
            // Try to load QR code from file
            const qrImg = document.getElementById('qrcode');
            const timestamp = new Date().getTime();
            qrImg.src = '/qr.png?' + timestamp;

            qrImg.onload = function() {
              qrImg.style.display = 'block';
              document.getElementById('qr-placeholder').style.display = 'none';
              document.getElementById('status').className = 'status loading';
              document.getElementById('status').innerHTML = '📱 QR code loaded - please scan with WhatsApp';
            };

            qrImg.onerror = function() {
              document.getElementById('qr-placeholder').innerHTML =
                '<p>⚠️ QR code not ready yet</p><p>Please wait and click refresh</p>';
              document.getElementById('status').className = 'status error';
              document.getElementById('status').innerHTML = '⏳ Waiting for QR code generation...';
            };
          }

          function refreshQR() {
            document.getElementById('qrcode').style.display = 'none';
            document.getElementById('qr-placeholder').style.display = 'block';
            document.getElementById('qr-placeholder').innerHTML =
              '<p>⏳ Refreshing QR code...</p><p>Please wait a moment.</p>';

            setTimeout(() => {
              checkStatus();
              loadQRCodeFallback();
            }, 1000);
          }

          // Auto-refresh status every 5 seconds
          checkInterval = setInterval(checkStatus, 5000);

          // Initial load
          setTimeout(() => {
            checkStatus();
            loadQRCodeFallback();
          }, 1000);
        </script>
      </body>
    </html>
  `);
});

// Generate a demo QR code for when WhatsApp client is not available
async function generateDemoQR() {
  try {
    // Generate a more realistic WhatsApp-like QR code format
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const demoWhatsAppData = `1@${randomId},${timestamp},demo,BPS-Pontianak-Bot`;

    currentQRCode = await QRCode.toDataURL(demoWhatsAppData, {
      width: 300,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      errorCorrectionLevel: "M",
    });

    // Also save as file for local development
    await QRCode.toFile("qr.png", demoWhatsAppData, {
      width: 300,
      margin: 2,
      errorCorrectionLevel: "M",
    });

    console.log("📱 Demo QR code generated for display");
    console.log(
      "⚠️ Note: This is a demo QR code. For real WhatsApp connection, ensure all dependencies are installed."
    );
  } catch (error) {
    console.error("❌ Failed to generate demo QR code:", error);
  }
}

// --- Start Express Server ---
if (isDevelopment) {
  app.listen(PORT, async () => {
    console.log(`🌐 Server running at: http://localhost:${PORT}`);
    console.log(`📱 Environment: Development`);

    if (client) {
      try {
        await client.initialize();
        console.log("🔄 WhatsApp client initialization started...");
      } catch (error) {
        console.error("❌ Failed to initialize WhatsApp client:", error);
        console.log("📱 Generating demo QR code instead...");
        await generateDemoQR();
      }
    } else {
      console.log(
        "📱 WhatsApp client not available - generating demo QR code..."
      );
      await generateDemoQR();
    }
  });
} else {
  // For Netlify, export the app
  module.exports = app;
  module.exports.handler = require("serverless-http")(app);
}
