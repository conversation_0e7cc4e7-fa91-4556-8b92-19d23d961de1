const fs = require("fs");
const path = require("path");
const express = require("express");
const QRCode = require("qrcode");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const { google } = require("googleapis");
require("dotenv").config();

// Environment detection
const isNetlify = process.env.NETLIFY === "true";
const isDevelopment = process.env.NODE_ENV !== "production" && !isNetlify;

// --- Gemini AI Init ---
const genAI = new GoogleGenerativeAI(process.env.API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

// --- Express Server Setup ---
const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.static(path.join(__dirname)));
app.use(express.json());

// Global variables for QR code and client state
let currentQRCode = null;
let clientReady = false;
let client = null;

// Only initialize WhatsApp client in development/local environment
if (isDevelopment) {
  const { Client, LocalAuth } = require("whatsapp-web.js");
  const qrcode = require("qrcode-terminal");

  // --- WhatsApp Client ---
  client = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    },
  });
}

// WhatsApp client event handlers (only in development)
if (isDevelopment && client) {
  client.on("qr", async (qr) => {
    console.log("📱 Scan this QR code with WhatsApp:");

    try {
      // Generate QR code as base64 for in-memory storage
      currentQRCode = await QRCode.toDataURL(qr);

      // Also save as file for local development
      await QRCode.toFile("qr.png", qr);
      console.log(
        "🖼️ QR code saved as qr.png – visit http://localhost:" +
          PORT +
          " to view."
      );
    } catch (err) {
      console.error("❌ Failed to generate QR code:", err);
    }
  });

  client.once("ready", () => {
    console.log("✅ WhatsApp bot is ready!");
    clientReady = true;
    currentQRCode = null; // Clear QR code when ready
  });

  client.on("disconnected", (reason) => {
    console.log("❌ WhatsApp client disconnected:", reason);
    clientReady = false;
    currentQRCode = null;
  });
}

// --- Google Sheets Config ---
const spreadsheetId = process.env.SPREADSHEET_ID;

function getIndonesiaDateTime() {
  const date = new Date();
  const options = {
    timeZone: "Asia/Jakarta",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  };

  const formatter = new Intl.DateTimeFormat("en-GB", options);
  const parts = formatter.formatToParts(date);

  const dateParts = {};
  for (const { type, value } of parts) {
    if (type !== "literal") {
      dateParts[type] = value;
    }
  }

  return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
}

// --- Real-Time RAG from Google Sheets ---
async function retrieveContext(prompt) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  const range = "RAG!A2:B";
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
  });

  const rows = response.data.values || [];
  const promptLower = prompt.toLowerCase();
  const matchedAnswers = [];

  for (const [keyword, answer] of rows) {
    const keywordLower = keyword.toLowerCase();
    if (promptLower.includes(keywordLower)) {
      matchedAnswers.push(`• ${answer}`);
    }
  }

  return matchedAnswers.length > 0
    ? matchedAnswers.join("\n\n")
    : "Maaf, saya hanya dapat membantu terkait informasi BPS Kota Pontianak. Silakan hubungi kontak resmi kami.";
}

// --- Append to Google Sheets (MESSAGE Sheet) ---
async function logMessageToSheet(sender, message, response) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: "MESSAGE!A2:D",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: [[sender, message, response, getIndonesiaDateTime()]],
    },
  });
}

// --- Message Handler (only in development) ---
if (isDevelopment && client) {
  client.on("message_create", async (message) => {
    try {
      if (message.fromMe) return;

      const prompt = message.body.toString().trim();
      if (!prompt) return;

      const sender = message.from;

      const context = await retrieveContext(prompt);

      const result = await model.generateContent(
        `Sebagai admin WhatsApp BPS Kota Pontianak, jawab pertanyaan ini berdasarkan konteks berikut:\n\n${context}\n\nPertanyaan:\n${prompt}`
      );
      const response = result.response.text();

      console.log("🟢 Replied to:", prompt);
      await client.sendMessage(message.from, response);
      console.log("🟢 Replied with:", response);

      await logMessageToSheet(sender, prompt, response);
    } catch (err) {
      console.error("❌ Error:", err);
      await client.sendMessage(
        message.from,
        "⚠️ Maaf, terjadi kesalahan. Silakan coba lagi nanti."
      );
    }
  });
}

// API Routes
app.get("/api/status", (req, res) => {
  res.json({
    environment: isNetlify ? "netlify" : "development",
    clientReady: clientReady,
    hasQRCode: !!currentQRCode,
  });
});

app.get("/api/qr", (req, res) => {
  if (currentQRCode) {
    res.json({ qrCode: currentQRCode });
  } else {
    res.status(404).json({ error: "No QR code available" });
  }
});

// Main route
app.get("/", (req, res) => {
  if (isNetlify) {
    // Netlify deployment - show setup instructions
    res.send(`
      <html>
        <head>
          <title>WhatsApp Chatbot BPS Pontianak - Setup</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              min-height: 100vh;
            }
            .container {
              background: rgba(255, 255, 255, 0.1);
              backdrop-filter: blur(10px);
              border-radius: 20px;
              padding: 40px;
              box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
              border: 1px solid rgba(255, 255, 255, 0.18);
            }
            h1 { text-align: center; margin-bottom: 30px; }
            .step {
              background: rgba(255, 255, 255, 0.1);
              padding: 20px;
              margin: 20px 0;
              border-radius: 10px;
              border-left: 4px solid #4CAF50;
            }
            .code {
              background: rgba(0, 0, 0, 0.3);
              padding: 15px;
              border-radius: 5px;
              font-family: 'Courier New', monospace;
              margin: 10px 0;
              overflow-x: auto;
            }
            .warning {
              background: rgba(255, 193, 7, 0.2);
              border-left-color: #FFC107;
            }
            .info {
              background: rgba(33, 150, 243, 0.2);
              border-left-color: #2196F3;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>

            <div class="step info">
              <h3>📋 Setup Instructions</h3>
              <p>This chatbot needs to run locally to connect to WhatsApp Web. Follow these steps:</p>
            </div>

            <div class="step">
              <h3>1. Clone the Repository</h3>
              <div class="code">git clone &lt;repository-url&gt;<br>cd whatsapp-chatbot-bps-6171</div>
            </div>

            <div class="step">
              <h3>2. Install Dependencies</h3>
              <div class="code">npm install</div>
            </div>

            <div class="step">
              <h3>3. Setup Environment Variables</h3>
              <p>Create a <code>.env</code> file with:</p>
              <div class="code">
                API_KEY=your_gemini_api_key<br>
                SPREADSHEET_ID=your_google_sheets_id<br>
                NODE_ENV=development
              </div>
            </div>

            <div class="step">
              <h3>4. Add Google Sheets Credentials</h3>
              <p>Place your <code>credentials.json</code> file in the project root.</p>
            </div>

            <div class="step">
              <h3>5. Run the Bot</h3>
              <div class="code">npm run dev</div>
              <p>Then visit <code>http://localhost:3000</code> to scan the QR code with WhatsApp.</p>
            </div>

            <div class="step warning">
              <h3>⚠️ Important Notes</h3>
              <ul>
                <li>The bot requires a persistent connection to WhatsApp Web</li>
                <li>It must run on a server or local machine, not on Netlify</li>
                <li>This deployment shows setup instructions only</li>
                <li>For production, consider using a VPS or dedicated server</li>
              </ul>
            </div>

            <div class="step info">
              <h3>📞 Contact Information</h3>
              <p>For support with BPS Pontianak services, please contact our official channels.</p>
            </div>
          </div>
        </body>
      </html>
    `);
  } else {
    // Local development - show QR code interface
    res.send(`
      <html>
        <head>
          <title>WhatsApp Chatbot - Scan QR Code</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              margin: 0;
              padding: 20px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              min-height: 100vh;
            }
            .container {
              background: rgba(255, 255, 255, 0.1);
              backdrop-filter: blur(10px);
              border-radius: 20px;
              padding: 40px;
              text-align: center;
              box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
              border: 1px solid rgba(255, 255, 255, 0.18);
              max-width: 500px;
              width: 100%;
            }
            #qrcode {
              border: 2px solid rgba(255, 255, 255, 0.3);
              padding: 20px;
              background: white;
              border-radius: 15px;
              margin: 20px 0;
              max-width: 100%;
              height: auto;
            }
            .status {
              padding: 15px;
              border-radius: 10px;
              margin: 15px 0;
              font-weight: bold;
            }
            .loading { background: rgba(255, 193, 7, 0.3); }
            .ready { background: rgba(76, 175, 80, 0.3); }
            .error { background: rgba(244, 67, 54, 0.3); }
            button {
              background: rgba(255, 255, 255, 0.2);
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: white;
              padding: 10px 20px;
              border-radius: 25px;
              cursor: pointer;
              font-size: 16px;
              margin: 10px;
              transition: all 0.3s ease;
            }
            button:hover {
              background: rgba(255, 255, 255, 0.3);
              transform: translateY(-2px);
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>
            <h2>📱 Scan QR Code with WhatsApp</h2>

            <div id="status" class="status loading">
              🔄 Initializing WhatsApp connection...
            </div>

            <div id="qr-container">
              <img id="qrcode" src="/qr.png" alt="QR Code" style="display: none;" />
              <div id="qr-placeholder">
                <p>⏳ Generating QR code...</p>
                <p>Please wait a moment.</p>
              </div>
            </div>

            <button onclick="refreshQR()">🔄 Refresh QR Code</button>
            <button onclick="checkStatus()">📊 Check Status</button>

            <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
              <p>📋 Instructions:</p>
              <ol style="text-align: left; max-width: 300px;">
                <li>Open WhatsApp on your phone</li>
                <li>Go to Settings → Linked Devices</li>
                <li>Tap "Link a Device"</li>
                <li>Scan the QR code above</li>
              </ol>
            </div>
          </div>

          <script>
            let checkInterval;

            function checkStatus() {
              fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                  const statusDiv = document.getElementById('status');
                  if (data.clientReady) {
                    statusDiv.className = 'status ready';
                    statusDiv.innerHTML = '✅ WhatsApp bot is ready and connected!';
                    document.getElementById('qr-container').style.display = 'none';
                    clearInterval(checkInterval);
                  } else if (data.hasQRCode) {
                    statusDiv.className = 'status loading';
                    statusDiv.innerHTML = '📱 Please scan the QR code with WhatsApp';
                    loadQRCode();
                  } else {
                    statusDiv.className = 'status loading';
                    statusDiv.innerHTML = '🔄 Generating QR code...';
                  }
                })
                .catch(error => {
                  console.error('Error:', error);
                  document.getElementById('status').className = 'status error';
                  document.getElementById('status').innerHTML = '❌ Connection error';
                });
            }

            function loadQRCode() {
              fetch('/api/qr')
                .then(response => response.json())
                .then(data => {
                  if (data.qrCode) {
                    const qrImg = document.getElementById('qrcode');
                    qrImg.src = data.qrCode;
                    qrImg.style.display = 'block';
                    document.getElementById('qr-placeholder').style.display = 'none';
                  }
                })
                .catch(error => {
                  console.error('QR Code error:', error);
                  // Fallback to file-based QR code
                  const qrImg = document.getElementById('qrcode');
                  qrImg.src = '/qr.png?' + new Date().getTime();
                  qrImg.style.display = 'block';
                  document.getElementById('qr-placeholder').style.display = 'none';
                });
            }

            function refreshQR() {
              location.reload();
            }

            // Auto-refresh status every 3 seconds
            checkInterval = setInterval(checkStatus, 3000);

            // Initial status check
            checkStatus();

            // Try to load QR code after 2 seconds
            setTimeout(loadQRCode, 2000);
          </script>
        </body>
      </html>
    `);
  }
});

// --- Start Express Server ---
if (isDevelopment) {
  app.listen(PORT, () => {
    if (client) {
      client.initialize();
    }
    console.log(`🌐 Server running at: http://localhost:${PORT}`);
    console.log(`📱 Environment: Development`);
  });
} else {
  // For Netlify, export the app
  module.exports = app;
  module.exports.handler = require("serverless-http")(app);
}
