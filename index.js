const fs = require("fs");
const path = require("path");
const express = require("express");
const QRCode = require("qrcode");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const { google } = require("googleapis");
require("dotenv").config();

// Environment detection
const isNetlify = process.env.NETLIFY === "true";
const isDevelopment = process.env.NODE_ENV !== "production" && !isNetlify;

console.log("🔧 Environment Configuration:");
console.log("- NODE_ENV:", process.env.NODE_ENV);
console.log("- NETLIFY:", process.env.NETLIFY);
console.log("- isNetlify:", isNetlify);
console.log("- isDevelopment:", isDevelopment);

// --- Gemini AI Init ---
const genAI = new GoogleGenerativeAI(process.env.API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

// --- Express Server Setup ---
const app = express();
const PORT = process.env.PORT || 3002;

app.use(express.static(path.join(__dirname)));
app.use(express.json());

// Global variables for QR code and client state
let currentQRCode = null;
let clientReady = false;
let client = null;

// Initialize WhatsApp client - ONLY REAL QR CODES
if (isDevelopment) {
  try {
    const { Client, LocalAuth } = require("whatsapp-web.js");
    const qrcode = require("qrcode-terminal");

    // --- WhatsApp Client - REAL CONNECTION ONLY ---
    client = new Client({
      authStrategy: new LocalAuth({
        dataPath: "./.wwebjs_auth",
      }),
      puppeteer: {
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
          "--disable-extensions",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
        ],
      },
      webVersionCache: {
        type: "remote",
        remotePath:
          "https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html",
      },
    });

    console.log("✅ WhatsApp Web.js initialized - REAL QR CODES ONLY");
  } catch (error) {
    console.error("❌ CRITICAL: WhatsApp Web.js not available:", error.message);
    console.log("� Cannot generate REAL QR codes without WhatsApp Web.js");
    console.log("📝 Run: npm install whatsapp-web.js puppeteer");
    process.exit(1); // Exit if can't create real WhatsApp connection
  }
}

// WhatsApp client event handlers - REAL QR CODES ONLY
console.log("🔧 Setting up REAL WhatsApp client event handlers...");

client.on("qr", async (qr) => {
  console.log("📱 REAL WhatsApp QR code received from WhatsApp servers!");
  console.log("🔍 QR code length:", qr.length);
  console.log("🔍 QR code preview:", qr.substring(0, 50) + "...");

  // Verify this is a real WhatsApp QR code
  const isRealWhatsApp =
    qr.length > 100 &&
    qr.includes("@") &&
    qr.includes(",") &&
    /^[0-9]@/.test(qr);

  if (!isRealWhatsApp) {
    console.error("❌ CRITICAL: Received invalid QR code format!");
    return;
  }

  console.log("✅ Verified: This is a REAL WhatsApp QR code!");

  try {
    // Generate QR code as base64 for in-memory storage
    currentQRCode = await QRCode.toDataURL(qr, {
      width: 300,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      errorCorrectionLevel: "M",
    });

    // Also save as file for local development
    await QRCode.toFile("qr.png", qr, {
      width: 300,
      margin: 2,
      errorCorrectionLevel: "M",
    });

    console.log("🖼️ REAL WhatsApp QR code generated successfully!");
    console.log("🌐 Visit http://localhost:" + PORT + " to scan the QR code");
    console.log("⏰ QR code will expire in ~20 seconds and regenerate");
    console.log("📱 This QR code WILL connect to WhatsApp when scanned!");
  } catch (err) {
    console.error("❌ Failed to generate QR code:", err);
  }
});

client.on("loading_screen", (percent, message) => {
  console.log("⏳ Loading WhatsApp Web:", percent + "%", message);
});

client.once("ready", () => {
  console.log("✅ WhatsApp bot is ready and connected!");
  clientReady = true;
  currentQRCode = null; // Clear QR code when ready
});

client.on("disconnected", (reason) => {
  console.log("❌ WhatsApp client disconnected:", reason);
  clientReady = false;
  currentQRCode = null;
});

client.on("auth_failure", (msg) => {
  console.error("❌ Authentication failed:", msg);
  clientReady = false;
  currentQRCode = null;
});

client.on("authenticated", () => {
  console.log("🔐 WhatsApp client authenticated successfully!");
});

// --- Google Sheets Config ---
const spreadsheetId = process.env.SPREADSHEET_ID;

function getIndonesiaDateTime() {
  const date = new Date();
  const options = {
    timeZone: "Asia/Jakarta",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  };

  const formatter = new Intl.DateTimeFormat("en-GB", options);
  const parts = formatter.formatToParts(date);

  const dateParts = {};
  for (const { type, value } of parts) {
    if (type !== "literal") {
      dateParts[type] = value;
    }
  }

  return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
}

// --- Real-Time RAG from Google Sheets ---
async function retrieveContext(prompt) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  const range = "RAG!A2:B";
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
  });

  const rows = response.data.values || [];
  const promptLower = prompt.toLowerCase();
  const matchedAnswers = [];

  for (const [keyword, answer] of rows) {
    const keywordLower = keyword.toLowerCase();
    if (promptLower.includes(keywordLower)) {
      matchedAnswers.push(`• ${answer}`);
    }
  }

  return matchedAnswers.length > 0
    ? matchedAnswers.join("\n\n")
    : "Maaf, saya hanya dapat membantu terkait informasi BPS Kota Pontianak. Silakan hubungi kontak resmi kami.";
}

// --- Append to Google Sheets (MESSAGE Sheet) ---
async function logMessageToSheet(sender, message, response) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: "MESSAGE!A2:D",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: [[sender, message, response, getIndonesiaDateTime()]],
    },
  });
}

// --- Message Handler (only in development) ---
if (isDevelopment && client) {
  client.on("message_create", async (message) => {
    try {
      if (message.fromMe) return;

      const prompt = message.body.toString().trim();
      if (!prompt) return;

      const sender = message.from;

      const context = await retrieveContext(prompt);

      const result = await model.generateContent(
        `Sebagai admin WhatsApp BPS Kota Pontianak, jawab pertanyaan ini berdasarkan konteks berikut:\n\n${context}\n\nPertanyaan:\n${prompt}`
      );
      const response = result.response.text();

      console.log("🟢 Replied to:", prompt);
      await client.sendMessage(message.from, response);
      console.log("🟢 Replied with:", response);

      await logMessageToSheet(sender, prompt, response);
    } catch (err) {
      console.error("❌ Error:", err);
      await client.sendMessage(
        message.from,
        "⚠️ Maaf, terjadi kesalahan. Silakan coba lagi nanti."
      );
    }
  });
}

// API Routes
app.get("/api/status", (req, res) => {
  res.json({
    environment: isNetlify ? "netlify" : "development",
    clientReady: clientReady,
    hasQRCode: !!currentQRCode,
  });
});

app.get("/api/qr", (req, res) => {
  if (currentQRCode) {
    res.json({ qrCode: currentQRCode });
  } else {
    res.status(404).json({ error: "No QR code available" });
  }
});

// Main route - QR Code Interface
app.get("/", (req, res) => {
  res.send(`
    <html>
      <head>
        <title>WhatsApp Chatbot - Scan QR Code</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 500px;
            width: 100%;
          }
          #qrcode {
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 20px;
            background: white;
            border-radius: 15px;
            margin: 20px 0;
            max-width: 300px;
            height: auto;
          }
          .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
          }
          .loading { background: rgba(255, 193, 7, 0.3); }
          .ready { background: rgba(76, 175, 80, 0.3); }
          .error { background: rgba(244, 67, 54, 0.3); }
          button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
          }
          button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
          }
          .qr-placeholder {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>
          <h2>📱 Scan QR Code with WhatsApp</h2>

          <div id="status" class="status loading">
            🔄 Initializing WhatsApp connection...
          </div>

          <div id="qr-container">
            <img id="qrcode" alt="QR Code" style="display: none;" />
            <div id="qr-placeholder" class="qr-placeholder">
              <p>⏳ Generating QR code...</p>
              <p>Please wait a moment.</p>
            </div>
          </div>

          <button onclick="refreshQR()">🔄 Refresh QR Code</button>
          <button onclick="checkStatus()">📊 Check Status</button>

          <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <div id="qr-instructions">
              <p>📋 Instructions:</p>
              <ol style="text-align: left; max-width: 300px;">
                <li>Open WhatsApp on your phone</li>
                <li>Go to Settings → Linked Devices</li>
                <li>Tap "Link a Device"</li>
                <li>Scan the QR code above</li>
              </ol>
            </div>

            <div id="demo-warning" style="display: none; background: rgba(255, 193, 7, 0.2); padding: 15px; border-radius: 10px; margin-top: 15px;">
              <p>⚠️ <strong>Demo QR Code</strong></p>
              <p style="font-size: 12px;">This is a demonstration QR code. For actual WhatsApp bot functionality, please run the application locally with all dependencies installed.</p>
            </div>
          </div>
        </div>

        <script>
          let checkInterval;

          function checkStatus() {
            fetch('/api/status')
              .then(response => response.json())
              .then(data => {
                const statusDiv = document.getElementById('status');
                if (data.clientReady) {
                  statusDiv.className = 'status ready';
                  statusDiv.innerHTML = '✅ WhatsApp bot is ready and connected!';
                  document.getElementById('qr-container').style.display = 'none';
                  document.getElementById('demo-warning').style.display = 'none';
                  clearInterval(checkInterval);
                } else if (data.hasQRCode) {
                  statusDiv.className = 'status loading';
                  statusDiv.innerHTML = '📱 Please scan the QR code with WhatsApp';
                  loadQRCode();
                  // Show demo warning if this is likely a demo QR code
                  if (data.environment === 'netlify' || !data.clientReady) {
                    document.getElementById('demo-warning').style.display = 'block';
                  }
                } else {
                  statusDiv.className = 'status loading';
                  statusDiv.innerHTML = '🔄 Generating QR code...';
                }
              })
              .catch(error => {
                console.error('Status error:', error);
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = '❌ Connection error - trying to load QR code directly';
                document.getElementById('demo-warning').style.display = 'block';
                loadQRCodeFallback();
              });
          }

          function loadQRCode() {
            fetch('/api/qr')
              .then(response => {
                if (!response.ok) {
                  throw new Error('QR API not available');
                }
                return response.json();
              })
              .then(data => {
                if (data.qrCode) {
                  const qrImg = document.getElementById('qrcode');
                  qrImg.src = data.qrCode;
                  qrImg.style.display = 'block';
                  document.getElementById('qr-placeholder').style.display = 'none';

                  // Update status to indicate this is a real QR code
                  document.getElementById('status').className = 'status loading';
                  document.getElementById('status').innerHTML = '📱 REAL WhatsApp QR code - scan quickly (expires in ~20 seconds)';

                  // Show expiration warning after 18 seconds
                  setTimeout(() => {
                    if (document.getElementById('status').innerHTML.includes('scan quickly')) {
                      document.getElementById('status').innerHTML = '⏰ QR code may have expired - click refresh for new code';
                      document.getElementById('status').className = 'status error';
                    }
                  }, 18000);
                }
              })
              .catch(error => {
                console.error('QR Code API error:', error);
                loadQRCodeFallback();
              });
          }

          function loadQRCodeFallback() {
            // Try to load QR code from file with cache busting
            const qrImg = document.getElementById('qrcode');
            const timestamp = new Date().getTime();
            qrImg.src = '/qr.png?' + timestamp;

            qrImg.onload = function() {
              qrImg.style.display = 'block';
              document.getElementById('qr-placeholder').style.display = 'none';
              document.getElementById('status').className = 'status loading';
              document.getElementById('status').innerHTML = '📱 REAL WhatsApp QR code - scan quickly (expires in ~20 seconds)';

              // Show warning about QR code expiration
              setTimeout(() => {
                if (document.getElementById('status').innerHTML.includes('scan quickly')) {
                  document.getElementById('status').innerHTML = '⏰ QR code may have expired - click refresh for new code';
                  document.getElementById('status').className = 'status error';
                }
              }, 18000); // 18 seconds
            };

            qrImg.onerror = function() {
              document.getElementById('qr-placeholder').innerHTML =
                '<p>⚠️ QR code not ready yet</p><p>Please wait and click refresh</p>';
              document.getElementById('status').className = 'status error';
              document.getElementById('status').innerHTML = '⏳ Waiting for QR code generation...';
            };
          }

          function refreshQR() {
            document.getElementById('qrcode').style.display = 'none';
            document.getElementById('qr-placeholder').style.display = 'block';
            document.getElementById('qr-placeholder').innerHTML =
              '<p>⏳ Refreshing QR code...</p><p>Please wait a moment.</p>';

            setTimeout(() => {
              checkStatus();
              loadQRCodeFallback();
            }, 1000);
          }

          // Auto-refresh status every 5 seconds
          checkInterval = setInterval(checkStatus, 5000);

          // Initial load
          setTimeout(() => {
            checkStatus();
            loadQRCodeFallback();
          }, 1000);
        </script>
      </body>
    </html>
  `);
});

// NO DEMO QR CODES - ONLY REAL WHATSAPP QR CODES

// --- Start Express Server - REAL WHATSAPP ONLY ---
app.listen(PORT, async () => {
  console.log(`🌐 Server running at: http://localhost:${PORT}`);
  console.log(`📱 REAL WhatsApp QR Codes ONLY - No Demo Codes!`);

  try {
    await client.initialize();
    console.log("🔄 REAL WhatsApp client initialization started...");
    console.log(
      "⏳ Waiting for REAL WhatsApp QR code from WhatsApp servers..."
    );
  } catch (error) {
    console.error("❌ CRITICAL: Failed to initialize WhatsApp client:", error);
    console.log("� Cannot generate REAL QR codes!");
    console.log("� Please ensure WhatsApp Web.js is properly installed");
    process.exit(1);
  }
});
