const serverless = require('serverless-http');
const express = require('express');
const QRCode = require('qrcode');

const app = express();
app.use(express.json());

// Mock data for Netlify deployment
let mockQRCode = null;

// Generate a demo QR code for display purposes
async function generateDemoQR() {
  try {
    const demoText = "This is a demo QR code. To use the actual WhatsApp bot, please run it locally following the setup instructions.";
    mockQRCode = await QRCode.toDataURL(demoText);
  } catch (error) {
    console.error('Error generating demo QR:', error);
  }
}

// Initialize demo QR code
generateDemoQR();

// API Routes for Netlify
app.get('/status', (req, res) => {
  res.json({
    environment: 'netlify',
    clientReady: false,
    hasQRCode: !!mockQRCode,
    message: 'This is a demo deployment. For actual WhatsApp functionality, run locally.'
  });
});

app.get('/qr', (req, res) => {
  if (mockQRCode) {
    res.json({ 
      qrCode: mockQRCode,
      demo: true,
      message: 'This is a demo QR code. For actual WhatsApp connection, run the bot locally.'
    });
  } else {
    res.status(404).json({ error: "Demo QR code not available" });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

module.exports.handler = serverless(app);
