2025/05/26-08:07:47.096 4bf4 Creating DB C:\laragon\www\whatsapp-chatbot-bps-6171\.wwebjs_auth_test\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/05/26-08:07:47.110 4bf4 Reusing MANIFEST C:\laragon\www\whatsapp-chatbot-bps-6171\.wwebjs_auth_test\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/05/26-08:07:47.829 355c Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.830 355c Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.831 355c Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.833 355c Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.835 355c Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.837 4bf4 Level-0 table #5: started
2025/05/26-08:07:47.843 4bf4 Level-0 table #5: 11975 bytes OK
2025/05/26-08:07:47.845 4bf4 Delete type=0 #3
2025/05/26-08:07:47.846 4bf4 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.847 54e0 Level-0 table #7: started
2025/05/26-08:07:47.854 54e0 Level-0 table #7: 967 bytes OK
2025/05/26-08:07:47.856 54e0 Delete type=0 #4
2025/05/26-08:07:47.857 54e0 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.857 54e0 Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x08\x00\x00\x05' @ 832 : 0
2025/05/26-08:07:47.857 54e0 Compacting 1@1 + 1@2 files
2025/05/26-08:07:47.861 54e0 Generated table #8@1: 215 keys, 4607 bytes
2025/05/26-08:07:47.861 54e0 Compacted 1@1 + 1@2 files => 4607 bytes
2025/05/26-08:07:47.863 54e0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/26-08:07:47.863 54e0 Delete type=2 #7
2025/05/26-08:07:47.864 54e0 Manual compaction at level-1 from '\x00\x08\x00\x00\x05' @ 832 : 0 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.865 3878 Level-0 table #10: started
2025/05/26-08:07:47.870 3878 Level-0 table #10: 249 bytes OK
2025/05/26-08:07:47.872 3878 Delete type=2 #5
2025/05/26-08:07:47.872 3878 Delete type=0 #6
2025/05/26-08:07:47.873 3878 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.873 3878 Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 838 : 0
2025/05/26-08:07:47.873 3878 Compacting 1@1 + 1@2 files
2025/05/26-08:07:47.875 3878 Generated table #11@1: 209 keys, 4584 bytes
2025/05/26-08:07:47.875 3878 Compacted 1@1 + 1@2 files => 4584 bytes
2025/05/26-08:07:47.877 3878 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/26-08:07:47.877 3878 Delete type=2 #10
2025/05/26-08:07:47.877 3878 Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 838 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.878 3878 Level-0 table #13: started
2025/05/26-08:07:47.882 3878 Level-0 table #13: 1545 bytes OK
2025/05/26-08:07:47.883 3878 Delete type=2 #8
2025/05/26-08:07:47.883 3878 Delete type=0 #9
2025/05/26-08:07:47.885 3878 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.885 3878 Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x02\x01\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 893 : 1
2025/05/26-08:07:47.885 3878 Compacting 1@1 + 1@2 files
2025/05/26-08:07:47.888 3878 Generated table #14@1: 258 keys, 5580 bytes
2025/05/26-08:07:47.888 3878 Compacted 1@1 + 1@2 files => 5580 bytes
2025/05/26-08:07:47.889 3878 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/26-08:07:47.890 3878 Delete type=2 #13
2025/05/26-08:07:47.890 3878 Manual compaction at level-1 from '\x00\x0e\x02\x01\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 893 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.891 3878 Level-0 table #16: started
2025/05/26-08:07:47.894 3878 Level-0 table #16: 351 bytes OK
2025/05/26-08:07:47.896 3878 Delete type=2 #11
2025/05/26-08:07:47.896 3878 Delete type=0 #12
2025/05/26-08:07:47.897 3878 Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.897 3878 Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 908 : 1
2025/05/26-08:07:47.897 3878 Compacting 1@1 + 1@2 files
2025/05/26-08:07:47.903 3878 Generated table #17@1: 255 keys, 5471 bytes
2025/05/26-08:07:47.903 3878 Compacted 1@1 + 1@2 files => 5471 bytes
2025/05/26-08:07:47.905 3878 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/26-08:07:47.905 3878 Delete type=2 #16
2025/05/26-08:07:47.905 3878 Manual compaction at level-1 from '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 908 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.906 3878 Level-0 table #19: started
2025/05/26-08:07:47.912 3878 Level-0 table #19: 1519 bytes OK
2025/05/26-08:07:47.913 3878 Delete type=2 #14
2025/05/26-08:07:47.913 3878 Delete type=0 #15
2025/05/26-08:07:47.914 3878 Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.914 3878 Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0f\x00\x00\xc8\x11\x00s\x00i\x00g\x00n\x00a\x00l\x00-\x00m\x00e\x00t\x00a\x00-\x00s\x00t\x00o\x00r\x00e' @ 955 : 1
2025/05/26-08:07:47.914 3878 Compacting 1@1 + 1@2 files
2025/05/26-08:07:47.921 3878 Generated table #20@1: 331 keys, 6634 bytes
2025/05/26-08:07:47.921 3878 Compacted 1@1 + 1@2 files => 6634 bytes
2025/05/26-08:07:47.922 3878 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/26-08:07:47.923 3878 Delete type=2 #19
2025/05/26-08:07:47.923 3878 Manual compaction at level-1 from '\x00\x0f\x00\x00\xc8\x11\x00s\x00i\x00g\x00n\x00a\x00l\x00-\x00m\x00e\x00t\x00a\x00-\x00s\x00t\x00o\x00r\x00e' @ 955 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:47.925 54e0 Level-0 table #22: started
2025/05/26-08:07:47.928 54e0 Level-0 table #22: 2615 bytes OK
2025/05/26-08:07:47.929 54e0 Delete type=2 #17
2025/05/26-08:07:47.929 54e0 Delete type=0 #18
2025/05/26-08:07:47.930 54e0 Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-08:07:50.877 3878 Compacting 1@1 + 1@2 files
2025/05/26-08:07:50.880 3878 Generated table #23@1: 292 keys, 6126 bytes
2025/05/26-08:07:50.880 3878 Compacted 1@1 + 1@2 files => 6126 bytes
2025/05/26-08:07:50.881 3878 compacted to: files[ 0 0 1 0 0 0 0 ]
